const { sequelize, User, <PERSON><PERSON>, MRIScan, Anamnesis, ScientificArticle, AIAnalysis, MedicalReport } = require('../models');
const bcrypt = require('bcryptjs');

async function setupDatabase() {
  try {
    console.log('🔄 Setting up database...');

    // Test connection
    await sequelize.authenticate();
    console.log('✅ Database connection established.');

    // Sync all models
    await sequelize.sync({ force: false }); // Set to true to recreate tables
    console.log('✅ Database models synchronized.');

    // Create default admin user if it doesn't exist
    const adminExists = await User.findOne({ where: { email: '<EMAIL>' } });
    
    if (!adminExists) {
      const hashedPassword = await bcrypt.hash('admin123', 12);
      await User.create({
        email: '<EMAIL>',
        password: hashedPassword,
        firstName: 'Admin',
        lastName: 'User',
        role: 'admin'
      });
      console.log('✅ Default admin user created (<EMAIL> / admin123)');
    }

    // Create sample doctor if it doesn't exist
    const doctorExists = await User.findOne({ where: { email: '<EMAIL>' } });
    
    if (!doctorExists) {
      const hashedPassword = await bcrypt.hash('doctor123', 12);
      const doctor = await User.create({
        email: '<EMAIL>',
        password: hashedPassword,
        firstName: 'Dr. John',
        lastName: 'Smith',
        role: 'doctor'
      });
      console.log('✅ Sample doctor user created (<EMAIL> / doctor123)');

      // Create sample patient
      const patient = await Patient.create({
        patientId: 'P001',
        firstName: 'Jane',
        lastName: 'Doe',
        dateOfBirth: '1985-06-15',
        gender: 'female',
        medicalHistory: 'No significant medical history',
        allergies: 'None known',
        currentMedications: 'None',
        doctorId: doctor.id
      });
      console.log('✅ Sample patient created');

      // Create sample anamnesis
      await Anamnesis.create({
        patientId: patient.id,
        chiefComplaint: 'Persistent headaches for the past 2 weeks',
        historyOfPresentIllness: 'Patient reports daily headaches, worse in the morning, associated with mild nausea',
        pastMedicalHistory: 'No significant past medical history',
        familyHistory: 'Mother had migraines',
        socialHistory: 'Non-smoker, occasional alcohol use',
        reviewOfSystems: {
          constitutional: ['fatigue'],
          neurological: ['headache', 'nausea'],
          cardiovascular: [],
          respiratory: []
        },
        symptoms: {
          pain_location: 'Bilateral temporal',
          pain_quality: 'Throbbing',
          aggravating_factors: 'Bright lights, stress',
          relieving_factors: 'Rest, dark room',
          associated_symptoms: 'Mild nausea'
        },
        duration: '2 weeks',
        severity: 6
      });
      console.log('✅ Sample anamnesis created');
    }

    // Create sample scientific articles if none exist
    const articleCount = await ScientificArticle.count();
    
    if (articleCount === 0) {
      const sampleArticles = [
        {
          title: 'Advanced MRI Techniques in Neurological Diagnosis',
          authors: ['Dr. Sarah Johnson', 'Dr. Michael Chen', 'Dr. Emily Rodriguez'],
          abstract: 'This study explores the latest advancements in MRI technology for neurological diagnosis, including diffusion tensor imaging and functional MRI applications.',
          journal: 'Journal of Neuroimaging',
          publicationDate: new Date('2023-08-15'),
          doi: '10.1234/jni.2023.001',
          keywords: ['mri', 'neuroimaging', 'diagnosis', 'brain', 'diffusion tensor imaging'],
          medicalTopic: 'Neurology',
          relevanceScore: 9.2
        },
        {
          title: 'Machine Learning Applications in Medical Image Analysis',
          authors: ['Dr. Alex Thompson', 'Dr. Lisa Wang'],
          abstract: 'A comprehensive review of machine learning techniques applied to medical image analysis, with focus on deep learning approaches for automated diagnosis.',
          journal: 'Medical AI Review',
          publicationDate: new Date('2023-09-20'),
          doi: '10.1234/mai.2023.002',
          keywords: ['machine learning', 'medical imaging', 'deep learning', 'automated diagnosis'],
          medicalTopic: 'Medical AI',
          relevanceScore: 8.8
        },
        {
          title: 'Clinical Decision Support Systems in Radiology',
          authors: ['Dr. Robert Kim', 'Dr. Maria Garcia', 'Dr. James Wilson'],
          abstract: 'An analysis of clinical decision support systems in radiology practice, examining their impact on diagnostic accuracy and workflow efficiency.',
          journal: 'Radiology Today',
          publicationDate: new Date('2023-07-10'),
          doi: '10.1234/rt.2023.003',
          keywords: ['clinical decision support', 'radiology', 'diagnostic accuracy', 'workflow'],
          medicalTopic: 'Radiology',
          relevanceScore: 8.5
        }
      ];

      for (const article of sampleArticles) {
        await ScientificArticle.create(article);
      }
      console.log('✅ Sample scientific articles created');
    }

    console.log('🎉 Database setup completed successfully!');
    console.log('\n📋 Summary:');
    console.log('- Admin user: <EMAIL> / admin123');
    console.log('- Doctor user: <EMAIL> / doctor123');
    console.log('- Sample patient and medical data created');
    console.log('- Scientific articles database populated');

  } catch (error) {
    console.error('❌ Database setup failed:', error);
    process.exit(1);
  } finally {
    await sequelize.close();
  }
}

// Run setup if this file is executed directly
if (require.main === module) {
  setupDatabase();
}

module.exports = setupDatabase;
