{"name": "medical-ai-platform", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "server": "node server/index.js", "server:dev": "nodemon server/index.js", "dev:full": "concurrently \"npm run dev\" \"npm run server:dev\"", "setup:db": "node server/scripts/setupDatabase.js"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-tabs": "^1.1.12", "@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.18", "@types/express": "^5.0.2", "@types/jsonwebtoken": "^9.0.9", "@types/multer": "^1.4.12", "@types/pg": "^8.15.2", "axios": "^1.9.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.511.0", "morgan": "^1.10.0", "multer": "^2.0.0", "next": "15.3.2", "pg": "^8.16.0", "pg-hstore": "^2.3.4", "react": "^19.0.0", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8", "recharts": "^2.15.3", "sequelize": "^6.37.7", "sharp": "^0.34.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "concurrently": "^9.1.2", "eslint": "^9", "eslint-config-next": "15.3.2", "nodemon": "^3.1.10", "tailwindcss": "^4", "typescript": "^5"}}