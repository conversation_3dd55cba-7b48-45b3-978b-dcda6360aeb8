# Medical AI Platform

A comprehensive multimodal AI platform for medical analysis and diagnosis, featuring advanced MRI analysis, patient anamnesis interpretation, literature search, and automated report generation using Ollama AI models.

## 🚀 Features

### Core Functionality
- **Multimodal AI Analysis**: Specialized AI agents for different medical tasks
- **MRI Image Analysis**: Advanced medical image processing and interpretation
- **Patient Anamnesis**: Structured patient history collection and analysis
- **Literature Search**: AI-powered scientific literature discovery and synthesis
- **Automated Reporting**: Comprehensive medical report generation
- **Real-time Collaboration**: Multi-user support with role-based access

### AI Agents
1. **MRI Analysis Agent**: Specialized in medical imaging interpretation
2. **Anamnesis Interpreter**: Patient history analysis and risk assessment
3. **Literature Review Agent**: Scientific article search and relevance scoring
4. **Diagnostic Assistant**: Comprehensive diagnostic synthesis
5. **Report Generator**: Professional medical report creation

### Technology Stack
- **Frontend**: Next.js 15, React, TypeScript, Tailwind CSS
- **Backend**: Node.js, Express.js, PostgreSQL
- **AI Integration**: Ollama API with multiple specialized models
- **Authentication**: JWT-based secure authentication
- **File Processing**: Sharp for image processing, Multer for uploads
- **Database**: PostgreSQL with Sequelize ORM

## 📋 Prerequisites

Before running this application, ensure you have:

1. **Node.js** (v18 or higher)
2. **PostgreSQL** (v12 or higher)
3. **Ollama** installed and running locally
4. **Git** for version control

### Ollama Setup

1. Install Ollama from [https://ollama.ai](https://ollama.ai)
2. Pull the required models:
   ```bash
   ollama pull llama3.2:latest
   ollama pull llama3.2-vision:latest
   ```
3. Ensure Ollama is running on `http://localhost:11434`

### PostgreSQL Setup

1. Install PostgreSQL
2. Create a database named `medical_ai_platform`
3. Create a user with appropriate permissions

## 🛠️ Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd medical-ai-platform
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment Configuration**
   ```bash
   cp .env.example .env
   ```

   Update the `.env` file with your configuration:
   ```env
   # Database Configuration
   DATABASE_URL=postgresql://username:password@localhost:5432/medical_ai_platform
   DB_HOST=localhost
   DB_PORT=5432
   DB_NAME=medical_ai_platform
   DB_USER=your_username
   DB_PASSWORD=your_password

   # Ollama Configuration
   OLLAMA_BASE_URL=http://localhost:11434
   OLLAMA_API_KEY=your_ollama_api_key_here

   # JWT Configuration
   JWT_SECRET=your_super_secret_jwt_key_here
   JWT_EXPIRES_IN=7d

   # Server Configuration
   PORT=3001
   NODE_ENV=development
   ```

4. **Database Setup**
   ```bash
   npm run setup:db
   ```

5. **Start the application**

   For development (runs both frontend and backend):
   ```bash
   npm run dev:full
   ```

   Or run separately:
   ```bash
   # Frontend only
   npm run dev

   # Backend only
   npm run server:dev
   ```

## 🔐 Default Credentials

After running the database setup, you can log in with:

- **Admin User**:
  - Email: `<EMAIL>`
  - Password: `admin123`

- **Doctor User**:
  - Email: `<EMAIL>`
  - Password: `doctor123`

## 📁 Project Structure

```
medical-ai-platform/
├── src/                          # Frontend source code
│   ├── app/                      # Next.js app directory
│   │   ├── page.tsx             # Main dashboard page
│   │   ├── layout.tsx           # Root layout
│   │   └── globals.css          # Global styles
├── server/                       # Backend source code
│   ├── index.js                 # Express server entry point
│   ├── config/                  # Configuration files
│   │   └── database.js          # Database configuration
│   ├── models/                  # Database models
│   │   └── index.js             # Sequelize models
│   ├── routes/                  # API routes
│   │   ├── auth.js              # Authentication routes
│   │   ├── patients.js          # Patient management
│   │   ├── mri.js               # MRI scan handling
│   │   ├── anamnesis.js         # Patient history
│   │   ├── articles.js          # Scientific articles
│   │   ├── aiAgents.js          # AI agent orchestration
│   │   └── reports.js           # Medical reports
│   ├── services/                # Business logic services
│   │   ├── ollamaService.js     # Ollama AI integration
│   │   └── aiAgentOrchestrator.js # AI agent coordination
│   ├── middleware/              # Express middleware
│   │   └── auth.js              # Authentication middleware
│   └── scripts/                 # Utility scripts
│       └── setupDatabase.js     # Database initialization
├── .env.example                 # Environment variables template
├── package.json                 # Dependencies and scripts
└── README.md                    # This file
```

## 🔌 API Endpoints

### Authentication
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `GET /api/auth/profile` - Get user profile
- `PUT /api/auth/profile` - Update user profile

### Patients
- `GET /api/patients` - List patients
- `POST /api/patients` - Create patient
- `GET /api/patients/:id` - Get patient details
- `PUT /api/patients/:id` - Update patient

### MRI Scans
- `POST /api/mri/upload` - Upload MRI scan
- `GET /api/mri/patient/:patientId` - Get patient's MRI scans
- `GET /api/mri/:id` - Get specific MRI scan
- `GET /api/mri/:id/image` - Get MRI image file

### AI Agents
- `GET /api/ai-agents/status` - Get AI agents status
- `POST /api/ai-agents/process-case` - Process complete medical case
- `POST /api/ai-agents/analyze-mri` - Analyze MRI scan only
- `POST /api/ai-agents/interpret-anamnesis` - Interpret patient history

### Scientific Articles
- `GET /api/articles/search` - Search scientific articles
- `GET /api/articles` - List articles
- `POST /api/articles` - Create article (admin only)

### Medical Reports
- `GET /api/reports/patient/:patientId` - Get patient reports
- `POST /api/reports` - Create medical report
- `GET /api/reports/:id` - Get specific report

## 🤖 AI Models Configuration

The platform uses multiple Ollama models for different tasks:

- **MRI Analysis**: `llama3.2-vision:latest` - For medical image analysis
- **Anamnesis**: `llama3.2:latest` - For patient history interpretation
- **Literature**: `llama3.2:latest` - For scientific literature search
- **Diagnostic**: `llama3.2:latest` - For diagnostic synthesis
- **Reports**: `llama3.2:latest` - For report generation

## 🔧 Development

### Adding New AI Agents

1. Create a new agent class in `server/services/aiAgentOrchestrator.js`
2. Extend the `BaseAgent` class
3. Implement the `analyze` method
4. Add the agent to the orchestrator
5. Create corresponding API endpoints

### Database Migrations

To modify the database schema:

1. Update models in `server/models/index.js`
2. Run database sync: `npm run setup:db`

## 🚀 Deployment

### Production Build

```bash
npm run build
npm start
```

### Environment Variables for Production

Ensure all environment variables are properly set for production:

- Set `NODE_ENV=production`
- Use secure JWT secrets
- Configure production database
- Set up proper CORS origins

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support and questions:

1. Check the documentation
2. Review existing issues
3. Create a new issue with detailed information

## 🔮 Future Enhancements

- DICOM file support for medical imaging
- Real-time collaboration features
- Advanced analytics dashboard
- Mobile application
- Integration with hospital systems (HL7 FHIR)
- Voice-to-text for anamnesis collection
- Multi-language support
