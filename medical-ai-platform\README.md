# Medical AI Platform

A comprehensive multimodal AI platform for medical analysis and diagnosis, featuring advanced MRI analysis, patient anamnesis interpretation, literature search, and automated report generation using Ollama AI models.

## 🚀 Features

### Core Functionality
- **Multimodal AI Analysis**: Specialized AI agents for different medical tasks
- **MRI Image Analysis**: Advanced medical image processing and interpretation
- **Patient Anamnesis**: Structured patient history collection and analysis
- **Literature Search**: AI-powered scientific literature discovery and synthesis
- **Automated Reporting**: Comprehensive medical report generation
- **Real-time Collaboration**: Multi-user support with role-based access

### AI Agents
1. **MRI Analysis Agent**: Specialized in medical imaging interpretation
2. **Anamnesis Interpreter**: Patient history analysis and risk assessment
3. **Literature Review Agent**: Scientific article search and relevance scoring
4. **Diagnostic Assistant**: Comprehensive diagnostic synthesis
5. **Report Generator**: Professional medical report creation

### Technology Stack
- **Frontend**: Next.js 15, React, TypeScript, Tailwind CSS
- **Backend**: Node.js, Express.js, PostgreSQL
- **AI Integration**: Ollama API with multiple specialized models
- **Authentication**: JWT-based secure authentication
- **File Processing**: Sharp for image processing, Multer for uploads
- **Database**: PostgreSQL with Sequelize ORM

## 📋 Prerequisites

Before running this application, ensure you have:

1. **Node.js** (v18 or higher)
2. **PostgreSQL** (v12 or higher)
3. **Ollama** installed and running locally
4. **Git** for version control

### Ollama Setup

1. Install Ollama from [https://ollama.ai](https://ollama.ai)
2. Pull the required models:
   ```bash
   ollama pull llama3.2:latest
   ollama pull llama3.2-vision:latest
   ```
3. Ensure Ollama is running on `http://localhost:11434`

### PostgreSQL Setup

1. Install PostgreSQL
2. Create a database named `medical_ai_platform`
3. Create a user with appropriate permissions

## 🛠️ Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd medical-ai-platform
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment Configuration**
   ```bash
   cp .env.example .env
   ```

   Update the `.env` file with your configuration:
   ```env
   # Database Configuration
   DATABASE_URL=postgresql://username:password@localhost:5432/medical_ai_platform
   DB_HOST=localhost
   DB_PORT=5432
   DB_NAME=medical_ai_platform
   DB_USER=your_username
   DB_PASSWORD=your_password

   # Ollama Configuration
   OLLAMA_BASE_URL=http://localhost:11434
   OLLAMA_API_KEY=your_ollama_api_key_here

   # JWT Configuration
   JWT_SECRET=your_super_secret_jwt_key_here
   JWT_EXPIRES_IN=7d

   # Server Configuration
   PORT=3001
   NODE_ENV=development
   ```

4. **Database Setup**
   ```bash
   npm run setup:db
   ```

5. **Start the application**

   For development (runs both frontend and backend):
   ```bash
   npm run dev:full
   ```

   Or run separately:
   ```bash
   # Frontend only
   npm run dev

   # Backend only
   npm run server:dev
   ```

## 🔐 Default Credentials

After running the database setup, you can log in with:

- **Admin User**:
  - Email: `<EMAIL>`
  - Password: `admin123`

- **Doctor User**:
  - Email: `<EMAIL>`
  - Password: `doctor123`
